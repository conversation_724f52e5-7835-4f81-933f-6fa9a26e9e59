.chatContainer {
  width: 400px;
  height: 600px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  background-color: #fff;
  overflow: hidden;
}

.chatHeader {
  background: linear-gradient(135deg, #e0c3fc 0%, #8ec5fc 100%);
  color: white;
  padding: 15px;
  text-align: center;
}

.messagesContainer {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  background-color: #f8f5ff;
}

.message {
  max-width: 80%;
  padding: 10px 15px;
  border-radius: 18px;
  word-wrap: break-word;
}

.userMessage {
  align-self: flex-end;
  background-color: #dcf8c6;
  border-bottom-right-radius: 4px;
}

.shecareMessage {
  align-self: flex-start;
  background-color: #f0d4ff;
  border-bottom-left-radius: 4px;
}

.inputForm {
  display: flex;
  padding: 10px;
  border-top: 1px solid #eee;
}

.textInput {
  flex: 1;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 20px;
  outline: none;
}

.sendButton {
  margin-left: 10px;
  padding: 0 20px;
  background-color: #b388ff;
  color: white;
  border: none;
  border-radius: 20px;
  cursor: pointer;
}

.loading {
  align-self: flex-start;
  font-style: italic;
  color: #888;
  margin: 5px 0;
}