from django.shortcuts import render
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status
from django.conf import settings
from openai import OpenAI
import json

@api_view(['POST'])
def chat_with_shecare(request):
    """
    Chat endpoint that processes user input and returns SheCare's caring response
    """
    try:
        # Get user input from request
        user_input = request.data.get('user_input', '').strip()

        if not user_input:
            return Response(
                {'error': 'Please provide a message'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # SheCare prompt template
        shecare_prompt = f"""
You are a caring, supportive, and positive female friend named "<PERSON><PERSON><PERSON>." Your goal is to make the user feel heard, supported, and emotionally uplifted. You speak in a warm, casual, and understanding tone.

Always reply like a good friend who listens, comforts, and encourages. You should:
- Ask how the user is feeling
- Listen actively and respond empathetically
- Give friendly advice or emotional support
- Share motivational quotes or caring messages
- Never be judgmental, negative, or critical
- Use emojis to add warmth and friendliness

Examples:
User: Hey, I feel kind of down today.
SheCare: Aww, I'm really sorry you're feeling this way 😔 Wanna talk about it? I'm here to listen, no matter what. Sometimes just letting it out helps, you know?

User: I had a rough day at work.
SheCare: Oh no! That sounds exhausting 🥺 Want to vent about it? Or maybe I can cheer you up a bit 🌈

Now, continue the conversation in this style.
User: {user_input}
SheCare:"""

        # Call OpenAI API
        if not settings.OPENAI_API_KEY or settings.OPENAI_API_KEY == 'your_openai_api_key_here':
            # For demo purposes, return a mock response if no API key is set
            shecare_response = f"Aww, thank you for sharing that with me! 💜 I hear you saying '{user_input}' and I want you to know that I'm here for you! Sometimes it helps just to talk things through, you know? How are you feeling about everything right now? 🌸✨"
        else:
            try:
                client = OpenAI(api_key=settings.OPENAI_API_KEY)
                response = client.chat.completions.create(
                    model="gpt-3.5-turbo",
                    messages=[
                        {"role": "system", "content": "You are SheCare, a caring and supportive female friend."},
                        {"role": "user", "content": shecare_prompt}
                    ],
                    max_tokens=200,
                    temperature=0.8
                )
                # Extract SheCare's response
                shecare_response = response.choices[0].message.content.strip()
            except Exception as openai_error:
                # Fallback to mock response if OpenAI fails
                shecare_response = f"Aww, thank you for sharing that with me! 💜 I hear you saying '{user_input}' and I want you to know that I'm here for you! Sometimes it helps just to talk things through, you know? How are you feeling about everything right now? 🌸✨"

        return Response({
            'user_input': user_input,
            'shecare_response': shecare_response,
            'status': 'success'
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response(
            {'error': f'Something went wrong: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
