'use client';

import { useState } from 'react';
import ChatBox from '@/components/ChatBox';

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 via-purple-50 to-indigo-50">
      {/* Header */}
      <div className="text-center py-8">
        <h1 className="text-4xl font-bold text-purple-800 mb-2">
          💜 SheCare 💜
        </h1>
        <p className="text-purple-600 text-lg">
          Your caring friend who's always here to listen 🌸
        </p>
      </div>

      {/* Chat Interface */}
      <div className="max-w-4xl mx-auto px-4 pb-8">
        <ChatBox />
      </div>

      {/* Floating Hearts Animation */}
      <div className="fixed inset-0 pointer-events-none overflow-hidden">
        <div className="absolute top-20 left-10 text-pink-300 text-2xl animate-bounce">💖</div>
        <div className="absolute top-40 right-20 text-purple-300 text-xl animate-pulse">🌸</div>
        <div className="absolute bottom-40 left-20 text-pink-300 text-lg animate-bounce delay-1000">✨</div>
        <div className="absolute bottom-20 right-10 text-purple-300 text-2xl animate-pulse delay-500">💜</div>
      </div>
    </div>
  );
}
