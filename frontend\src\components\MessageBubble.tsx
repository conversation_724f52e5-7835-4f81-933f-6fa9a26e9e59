'use client';

interface Message {
  id: number;
  text: string;
  isUser: boolean;
  timestamp: Date;
}

interface MessageBubbleProps {
  message: Message;
}

export default function MessageBubble({ message }: MessageBubbleProps) {
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  if (message.isUser) {
    return (
      <div className="flex justify-end">
        <div className="max-w-xs lg:max-w-md">
          <div className="bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-2xl rounded-br-md px-4 py-3 shadow-lg">
            <p className="text-sm leading-relaxed">{message.text}</p>
          </div>
          <p className="text-xs text-purple-400 mt-1 text-right">
            {formatTime(message.timestamp)}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex justify-start">
      <div className="max-w-xs lg:max-w-md">
        <div className="flex items-start space-x-2">
          <div className="w-8 h-8 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full flex items-center justify-center text-white text-sm font-semibold flex-shrink-0">
            💜
          </div>
          <div>
            <div className="bg-white border border-purple-100 rounded-2xl rounded-bl-md px-4 py-3 shadow-lg">
              <p className="text-sm leading-relaxed text-gray-800">{message.text}</p>
            </div>
            <p className="text-xs text-purple-400 mt-1 ml-2">
              SheCare • {formatTime(message.timestamp)}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
