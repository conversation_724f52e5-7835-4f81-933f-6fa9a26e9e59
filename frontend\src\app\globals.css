@import "tailwindcss";

:root {
  --background: #fdf2f8;
  --foreground: #4c1d95;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #fdf2f8;
    --foreground: #4c1d95;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Custom scrollbar for chat */
.chat-scroll::-webkit-scrollbar {
  width: 6px;
}

.chat-scroll::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 10px;
}

.chat-scroll::-webkit-scrollbar-thumb {
  background: #c084fc;
  border-radius: 10px;
}

.chat-scroll::-webkit-scrollbar-thumb:hover {
  background: #a855f7;
}

/* Floating animation for hearts */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}
